from flasgger import Swagger
from flask import Flask, request, make_response
from app.auth import auth_bp
from app.config import Config
from app.helpers.extensions import init_extensions

from app.routes import main_bp
from app.tasks.routes import tasks_bp
from app.users.routes import users_bp
from app.projects.routes import projects_bp
from app.members.routes import members_bp
from app.statistics.routes import statistics_bp


def create_app(test_config=None):
    app = Flask(__name__)
    app.config.from_object(Config)

    if test_config:
        app.config.update(test_config)
    init_extensions(app)

    # Register blueprints
    app.register_blueprint(main_bp)
    app.register_blueprint(tasks_bp)
    app.register_blueprint(users_bp)
    app.register_blueprint(projects_bp)
    app.register_blueprint(members_bp)
    app.register_blueprint(statistics_bp)
    app.register_blueprint(auth_bp)

    # Initialize Swagger after all blueprints are registered
    try:
        Swagger(app, template_file="./swagger/openapi.yaml")
        print("✅ Swagger initialized successfully")
    except ImportError as e:
        print(f"❌ Swagger not available: {e}")
        print("💡 Install flasgger: pip install flasgger")
    except Exception as e:
        print(f"❌ Swagger initialization failed: {e}")

    return app
