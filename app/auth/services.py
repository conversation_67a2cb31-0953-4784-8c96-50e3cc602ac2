from werkzeug.security import generate_password_hash

from app.models.api_response import ApiResponse
from app.helpers.jwt_blocklist import jwt_blocklist
from app.helpers.validators import is_valid_email, is_strong_password
from app.models.user import User
from app.helpers.extensions import db


def create_user(data):
    username = data.get("username", "").strip()
    email = data.get("email", "").strip()
    password = data.get("password", "")

    if not username or not email or not password:
        return ApiResponse.failure(
            "Username, email and password are required.", code=400
        )

    if username == "" or email == "":
        return ApiResponse.failure(
            "Username and email must not be empty or spaces only.", code=400
        )

    if not is_valid_email(email):
        return ApiResponse.failure("Invalid email format.", code=400)

    if not is_strong_password(password):
        return ApiResponse.failure(
            "Password must be at least 8 characters and include "
            "uppercase, lowercase, number, and special character.",
            code=400,
        )

    if User.query.filter_by(username=username).first():
        return ApiResponse.failure("Username already exists.", code=400)

    existing_user = User.query.filter_by(email=email).first()
    if existing_user:
        return ApiResponse.failure("Email already exists.", code=400)

    new_user = User(
        username=username,
        email=email,
        password_hash=generate_password_hash(password),
        auth_provider='local'
    )
    db.session.add(new_user)
    db.session.commit()
    return ApiResponse.success("User created successfully", code=201)


def get_user_by_email(email):
    return User.query.filter_by(email=email).first()


def get_user_by_id(user_id):
    return User.query.get(user_id)


def check_if_token_revoked(jwt_header, jwt_payload):
    jti = jwt_payload["jti"]
    return jti in jwt_blocklist
