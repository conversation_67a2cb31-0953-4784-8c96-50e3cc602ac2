from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import get_project_members,add_project_member, update_member_role, remove_project_member, get_user_projects

members_bp = Blueprint("members_bp", __name__, url_prefix="/members")


@members_bp.route("/projects/<int:project_id>", methods=["GET"])
@jwt_required()
def get_members(project_id):
    """Get all members of a project"""
    current_user_id = int(get_jwt_identity())
    result, status = get_project_members(project_id, current_user_id)
    return jsonify(result), status

@members_bp.route("/projects/<int:project_id>", methods=["POST"])
@jwt_required()
def add_member(project_id):
    """Add a member to project"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({"message": "No data provided"}), 400

    result, status = add_project_member(project_id, current_user_id, data)
    return jsonify(result), status

@members_bp.route("/projects/<int:project_id>/users/<int:member_user_id>", methods=["PUT"])
@jwt_required()
def update_member(project_id, member_user_id):
    """Update a member's role in project"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({"message": "No data provided"}), 400

    result, status = update_member_role(project_id, current_user_id, member_user_id, data)
    return jsonify(result), status

@members_bp.route("/projects/<int:project_id>/users/<int:member_user_id>", methods=["DELETE"])
@jwt_required()
def remove_member(project_id, member_user_id):
    """Remove a member from project"""
    current_user_id = int(get_jwt_identity())
    result, status = remove_project_member(project_id, current_user_id, member_user_id)
    return jsonify(result), status

@members_bp.route("/my-projects", methods=["GET"])
@jwt_required()
def get_my_projects():
    """Get all projects where current user is a member or creator"""
    current_user_id = int(get_jwt_identity())
    result, status = get_user_projects(current_user_id)
    return jsonify(result), status
