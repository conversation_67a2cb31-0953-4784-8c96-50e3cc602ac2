from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import get_project_members,add_project_member, update_member_role, remove_project_member, get_user_projects

members_bp = Blueprint("members_bp", __name__, url_prefix="/members")


@members_bp.route("/projects/<int:project_id>", methods=["GET"])
@jwt_required()
def get_members(project_id):
    """
    Get all members of a project
    ---
    tags:
      - Project Members
    security:
      - BearerAuth: []
    produces:
      - application/json
    parameters:
      - in: path
        name: project_id
        required: true
        type: integer
        description: ID of the project
    responses:
      200:
        description: List of project members
        schema:
          type: object
          properties:
            project_id:
              type: integer
            project_name:
              type: string
            members:
              type: array
              items:
                type: object
                properties:
                  user_id:
                    type: integer
                  username:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                  role_description:
                    type: string
                  joined_at:
                    type: string
                    format: date-time
            total_members:
              type: integer
      403:
        description: Access denied
      404:
        description: Project not found
    """
    current_user_id = int(get_jwt_identity())
    result, status = get_project_members(project_id, current_user_id)
    return jsonify(result), status

@members_bp.route("/projects/<int:project_id>", methods=["POST"])
@jwt_required()
def add_member(project_id):
    """
    Add a member to project
    ---
    tags:
      - Project Members
    security:
      - BearerAuth: []
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: path
        name: project_id
        required: true
        type: integer
        description: ID of the project
      - in: body
        name: body
        required: true
        schema:
          type: object
          properties:
            user_id:
              type: integer
              example: 2
              description: ID of user to add as member
            role:
              type: string
              enum: [project_member, project_viewer]
              default: project_member
              example: "project_member"
              description: Role to assign to the member
          required:
            - user_id
    responses:
      201:
        description: Member added successfully
        schema:
          type: object
          properties:
            message:
              type: string
            member:
              type: object
      400:
        description: Invalid input or user already member
      403:
        description: Permission denied
      404:
        description: Project or user not found
    """
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({"message": "No data provided"}), 400

    result, status = add_project_member(project_id, current_user_id, data)
    return jsonify(result), status

@members_bp.route("/projects/<int:project_id>/users/<int:member_user_id>", methods=["PUT"])
@jwt_required()
def update_member(project_id, member_user_id):
    """
    Update a member's role in project
    ---
    tags:
      - Project Members
    security:
      - BearerAuth: []
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: path
        name: project_id
        required: true
        type: integer
        description: ID of the project
      - in: path
        name: member_user_id
        required: true
        type: integer
        description: ID of the member to update
      - in: body
        name: body
        required: true
        schema:
          type: object
          properties:
            role:
              type: string
              enum: [project_member, project_viewer]
              example: "project_viewer"
              description: New role for the member
          required:
            - role
    responses:
      200:
        description: Member role updated successfully
      400:
        description: Invalid input
      403:
        description: Permission denied
      404:
        description: Project or member not found
    """
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({"message": "No data provided"}), 400

    result, status = update_member_role(project_id, current_user_id, member_user_id, data)
    return jsonify(result), status

@members_bp.route("/projects/<int:project_id>/users/<int:member_user_id>", methods=["DELETE"])
@jwt_required()
def remove_member(project_id, member_user_id):
    """
    Remove a member from project
    ---
    tags:
      - Project Members
    security:
      - BearerAuth: []
    produces:
      - application/json
    parameters:
      - in: path
        name: project_id
        required: true
        type: integer
        description: ID of the project
      - in: path
        name: member_user_id
        required: true
        type: integer
        description: ID of the member to remove
    responses:
      200:
        description: Member removed successfully
      400:
        description: Cannot remove project creator
      403:
        description: Permission denied
      404:
        description: Project or member not found
    """
    current_user_id = int(get_jwt_identity())
    result, status = remove_project_member(project_id, current_user_id, member_user_id)
    return jsonify(result), status

@members_bp.route("/my-projects", methods=["GET"])
@jwt_required()
def get_my_projects():
    """
    Get all projects where current user is a member or creator
    ---
    tags:
      - Project Members
    security:
      - BearerAuth: []
    produces:
      - application/json
    responses:
      200:
        description: List of user's projects
        schema:
          type: object
          properties:
            projects:
              type: array
              items:
                type: object
                properties:
                  project_id:
                    type: integer
                  name:
                    type: string
                  description:
                    type: string
                  status:
                    type: string
                  priority:
                    type: string
                  role:
                    type: string
                  is_creator:
                    type: boolean
                  created_at:
                    type: string
                    format: date-time
                  joined_at:
                    type: string
                    format: date-time
            total_projects:
              type: integer
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    result, status = get_user_projects(current_user_id)
    return jsonify(result), status
