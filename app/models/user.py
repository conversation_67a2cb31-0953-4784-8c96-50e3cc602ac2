"""
User-related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime


class User(db.Model):
    """
    User model for authentication and user management
    """
    __tablename__ = 'user'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=True)  # Made nullable for OAuth users
    username = db.Column(db.String(100), nullable=False)
    two_factor_enabled = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    bio = db.Column(db.Text, nullable=True)

    # GitHub OAuth fields
    github_id = db.Column(db.String(50), unique=True, nullable=True)
    github_username = db.Column(db.String(100), nullable=True)
    github_avatar_url = db.Column(db.String(255), nullable=True)
    auth_provider = db.Column(db.String(50), default='local')  # 'local', 'github', etc.

    def to_dict(self):
        """Convert user object to dictionary"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "phone": self.phone,
            "bio": self.bio,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "two_factor_enabled": self.two_factor_enabled,
            "github_id": self.github_id,
            "github_username": self.github_username,
            "github_avatar_url": self.github_avatar_url,
            "auth_provider": self.auth_provider,
        }

    def __repr__(self):
        return f"<User {self.username}>"


class UserRole(db.Model):
    """
    Many-to-many relationship between Users and Roles
    """
    __tablename__ = 'user_roles'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship("User", backref=db.backref("user_roles", lazy=True))
    role = db.relationship("Role", backref=db.backref("user_roles", lazy=True))

    def __repr__(self):
        return f"<UserRole user_id={self.user_id} role_id={self.role_id}>"
