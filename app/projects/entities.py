from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class ProjectEntity:
    """Project entity representing the business object"""
    id: Optional[int] = None
    name: str = ""
    description: Optional[str] = None
    status: str = "active"
    priority: str = "medium"
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    created_by: Optional[int] = None
    user_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    deleted_at: Optional[datetime] = None

    def to_dict(self) -> dict:
        """Convert entity to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'status': self.status,
            'priority': self.priority,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'created_by': self.created_by,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'deleted_at': self.deleted_at.isoformat() if self.deleted_at else None
        }

    @classmethod
    def from_model(cls, model):
        """Create entity from SQLAlchemy model"""
        if not model:
            return None
        
        return cls(
            id=model.id,
            name=model.name,
            description=model.description,
            status=getattr(model, 'status', 'active'),
            priority=getattr(model, 'priority', 'medium'),
            start_date=getattr(model, 'start_date', None),
            end_date=getattr(model, 'end_date', None),
            created_by=model.created_by,
            user_id=getattr(model, 'user_id', None),
            created_at=model.created_at,
            updated_at=getattr(model, 'updated_at', None),
            deleted_at=getattr(model, 'deleted_at', None)
        )

    def is_valid(self) -> tuple[bool, str]:
        """Validate entity data"""
        if not self.name or len(self.name.strip()) == 0:
            return False, "Project name is required"
        
        if len(self.name) > 255:
            return False, "Project name must be less than 255 characters"
        
        if self.status not in ['active', 'inactive', 'completed', 'on_hold']:
            return False, "Invalid status. Must be one of: active, inactive, completed, on_hold"
        
        if self.priority not in ['low', 'medium', 'high', 'urgent']:
            return False, "Invalid priority. Must be one of: low, medium, high, urgent"
        
        if self.start_date and self.end_date and self.start_date > self.end_date:
            return False, "Start date cannot be after end date"
        
        return True, ""
