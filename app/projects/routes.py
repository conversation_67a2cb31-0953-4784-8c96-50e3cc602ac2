from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import get_all_projects, get_project_by_id, create_project, update_project, delete_project, restore_project
from app.models.api_response import ApiResponse

projects_bp = Blueprint("projects_bp", __name__, url_prefix="/projects")


@projects_bp.route("/", methods=["GET"])
@jwt_required()
def list_projects():
    """Get all projects for the current user"""
    current_user_id = int(get_jwt_identity())
    include_deleted = request.args.get('include_deleted', 'false').lower() == 'true'
    
    result, status = get_all_projects(current_user_id, include_deleted)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["GET"])
@jwt_required()
def get_project(project_id):
    """Get a specific project by ID"""
    current_user_id = int(get_jwt_identity())
    result, status = get_project_by_id(project_id, current_user_id)
    return jsonify(result), status


@projects_bp.route("/", methods=["POST"])
@jwt_required()
def create_project_endpoint():
    """Create a new project"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        response = ApiResponse.failure("No data provided", code=400)
        return jsonify(response.to_dict()), response.code

    result, status = create_project(data, current_user_id)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["PUT"])
@jwt_required()
def update_project_endpoint(project_id):
    """Update an existing project"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        response = ApiResponse.failure("No data provided", code=400)
        return jsonify(response.to_dict()), response.code

    result, status = update_project(project_id, data, current_user_id)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["DELETE"])
@jwt_required()
def delete_project_endpoint(project_id):
    """Delete a project (soft delete by default)"""
    current_user_id = int(get_jwt_identity())
    hard_delete = request.args.get('hard_delete', 'false').lower() == 'true'

    result, status = delete_project(project_id, current_user_id, hard_delete)
    return jsonify(result), status

@projects_bp.route("/<int:project_id>/restore", methods=["POST"])
@jwt_required()
def restore_project_endpoint(project_id):
    """Restore a soft-deleted project"""
    current_user_id = int(get_jwt_identity())
    result, status = restore_project(project_id, current_user_id)
    return jsonify(result), status