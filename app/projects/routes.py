from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import get_all_projects, get_project_by_id, create_project, update_project, delete_project, restore_project

projects_bp = Blueprint("projects_bp", __name__, url_prefix="/projects")


@projects_bp.route("/", methods=["GET"])
@jwt_required()
def list_projects():
    """
    Get all projects for the current user
    ---
    tags:
      - Projects
    security:
      - BearerAuth: []
    produces:
      - application/json
    parameters:
      - name: include_deleted
        in: query
        type: boolean
        required: false
        description: Include soft-deleted projects in the response
        default: false
    responses:
      200:
        description: List of projects
        schema:
          type: object
          properties:
            projects:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: integer
                  name:
                    type: string
                  description:
                    type: string
                  status:
                    type: string
                    enum: [active, inactive, completed, on_hold]
                  priority:
                    type: string
                    enum: [low, medium, high, urgent]
                  start_date:
                    type: string
                    format: date
                  end_date:
                    type: string
                    format: date
                  created_by:
                    type: integer
                  user_id:
                    type: integer
                  created_at:
                    type: string
                    format: date-time
                  updated_at:
                    type: string
                    format: date-time
                  deleted_at:
                    type: string
                    format: date-time
            count:
              type: integer
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    include_deleted = request.args.get('include_deleted', 'false').lower() == 'true'
    
    result, status = get_all_projects(current_user_id, include_deleted)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["GET"])
@jwt_required()
def get_project(project_id):
    """
    Get a specific project by ID
    ---
    tags:
      - Projects
    security:
      - BearerAuth: []
    produces:
      - application/json
    parameters:
      - in: path
        name: project_id
        required: true
        type: integer
        description: ID of the project to retrieve
    responses:
      200:
        description: Project details
        schema:
          type: object
          properties:
            project:
              type: object
              properties:
                id:
                  type: integer
                name:
                  type: string
                description:
                  type: string
                status:
                  type: string
                  enum: [active, inactive, completed, on_hold]
                priority:
                  type: string
                  enum: [low, medium, high, urgent]
                start_date:
                  type: string
                  format: date
                end_date:
                  type: string
                  format: date
                created_by:
                  type: integer
                user_id:
                  type: integer
                created_at:
                  type: string
                  format: date-time
                updated_at:
                  type: string
                  format: date-time
      404:
        description: Project not found
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    result, status = get_project_by_id(project_id, current_user_id)
    return jsonify(result), status


@projects_bp.route("/", methods=["POST"])
@jwt_required()
def create_project_endpoint():
    """
    Create a new project
    ---
    tags:
      - Projects
    security:
      - BearerAuth: []
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        schema:
          type: object
          properties:
            name:
              type: string
              example: "My New Project"
              description: Project name (required)
            description:
              type: string
              example: "This is a sample project description"
              description: Project description
            status:
              type: string
              enum: [active, inactive, completed, on_hold]
              default: active
              example: "active"
            priority:
              type: string
              enum: [low, medium, high, urgent]
              default: medium
              example: "high"
            start_date:
              type: string
              format: date
              example: "2024-01-01"
              description: Project start date (YYYY-MM-DD)
            end_date:
              type: string
              format: date
              example: "2024-12-31"
              description: Project end date (YYYY-MM-DD)
            user_id:
              type: integer
              example: 1
              description: Project owner ID (defaults to creator)
          required:
            - name
    responses:
      201:
        description: Project created successfully
        schema:
          type: object
          properties:
            message:
              type: string
            project:
              type: object
      400:
        description: Invalid input data
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({"message": "No data provided"}), 400

    result, status = create_project(data, current_user_id)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["PUT"])
@jwt_required()
def update_project_endpoint(project_id):
    """
    Update an existing project
    ---
    tags:
      - Projects
    security:
      - BearerAuth: []
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: path
        name: project_id
        required: true
        type: integer
        description: ID of the project to update
      - in: body
        name: body
        required: true
        schema:
          type: object
          properties:
            name:
              type: string
              example: "Updated Project Name"
            description:
              type: string
              example: "Updated project description"
            status:
              type: string
              enum: [active, inactive, completed, on_hold]
              example: "completed"
            priority:
              type: string
              enum: [low, medium, high, urgent]
              example: "urgent"
            start_date:
              type: string
              format: date
              example: "2024-02-01"
            end_date:
              type: string
              format: date
              example: "2024-11-30"
            user_id:
              type: integer
              example: 2
              description: New project owner ID
    responses:
      200:
        description: Project updated successfully
        schema:
          type: object
          properties:
            message:
              type: string
            project:
              type: object
      400:
        description: Invalid input data
      404:
        description: Project not found
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({"message": "No data provided"}), 400

    result, status = update_project(project_id, data, current_user_id)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["DELETE"])
@jwt_required()
def delete_project_endpoint(project_id):
    """
    Delete a project (soft delete by default)
    ---
    tags:
      - Projects
    security:
      - BearerAuth: []
    produces:
      - application/json
    parameters:
      - in: path
        name: project_id
        required: true
        type: integer
        description: ID of the project to delete
      - name: hard_delete
        in: query
        type: boolean
        required: false
        description: Permanently delete the project (cannot be restored)
        default: false
    responses:
      200:
        description: Project deleted successfully
        schema:
          type: object
          properties:
            message:
              type: string
      404:
        description: Project not found or no permission
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    hard_delete = request.args.get('hard_delete', 'false').lower() == 'true'

    result, status = delete_project(project_id, current_user_id, hard_delete)
    return jsonify(result), status

@projects_bp.route("/<int:project_id>/restore", methods=["POST"])
@jwt_required()
def restore_project_endpoint(project_id):
    """
    Restore a soft-deleted project
    ---
    tags:
      - Projects
    security:
      - BearerAuth: []
    produces:
      - application/json
    parameters:
      - in: path
        name: project_id
        required: true
        type: integer
        description: ID of the project to restore
    responses:
      200:
        description: Project restored successfully
        schema:
          type: object
          properties:
            message:
              type: string
            project:
              type: object
      404:
        description: Deleted project not found
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    result, status = restore_project(project_id, current_user_id)
    return jsonify(result), status