from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import get_all_tasks, create_task, update_task, delete_task

tasks_bp = Blueprint("tasks_bp", __name__, url_prefix="/tasks")


@tasks_bp.route("/list_tasks", methods=["GET"])
@jwt_required()
def list_tasks():
    current_user_id = get_jwt_identity()
    assignee_id = request.args.get("assignee_id", type=int)
    response = get_all_tasks(current_user_id, assignee_id)
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/create_task_view", methods=["POST"])
@jwt_required()
def create_task_view():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    response = create_task(data, current_user_id)
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/update_task_view/<int:task_id>", methods=["PUT"])
@jwt_required()
def update_task_view(task_id):
    current_user_id = get_jwt_identity()
    data = request.get_json()
    response = update_task(task_id, data, current_user_id)
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/delete_task_view/<int:task_id>", methods=["DELETE"])
@jwt_required()
def delete_task_view(task_id):
    current_user_id = get_jwt_identity()
    response = delete_task(task_id, current_user_id)
    return jsonify(response.to_dict()), response.code
