import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

from flask import Flask
from flask_migrate import upgrade, migrate as flask_migrate, init as flask_init, stamp
from app.config import Config
from app.helpers.extensions import db, migrate
from app.models import User, Task, Project, Role, Permission, RolePermission, UserProject


def init_basic_roles_and_permissions():
    """Initialize basic roles and permissions"""
    try:
        # Create basic project roles
        roles_to_create = [
            {"name": "project_owner", "description": "Project owner with full permissions"},
            {"name": "project_member", "description": "Project member with limited permissions"},
            {"name": "project_viewer", "description": "Project viewer with read-only access"},
        ]

        for role_data in roles_to_create:
            existing_role = Role.query.filter_by(name=role_data["name"]).first()
            if not existing_role:
                role = Role(
                    name=role_data["name"],
                    description=role_data["description"],
                    created_by="system"
                )
                db.session.add(role)
                print(f"  ✅ Created role: {role_data['name']}")
            else:
                print(f"  ⚠️  Role already exists: {role_data['name']}")

        # Create basic permissions
        permissions_to_create = [
            {"name": "view_project", "description": "View project details"},
            {"name": "edit_project", "description": "Edit project settings"},
            {"name": "delete_project", "description": "Delete project"},
            {"name": "manage_project_members", "description": "Manage project members"},
            {"name": "create_task", "description": "Create new tasks"},
            {"name": "edit_task", "description": "Edit tasks"},
            {"name": "delete_task", "description": "Delete tasks"},
            {"name": "assign_task", "description": "Assign tasks to users"},
            {"name": "add_comment", "description": "Add comments"},
            {"name": "edit_comment", "description": "Edit comments"},
            {"name": "delete_comment", "description": "Delete comments"},
            {"name": "upload_attachment", "description": "Upload attachments"},
            {"name": "delete_attachment", "description": "Delete attachments"},
        ]

        for perm_data in permissions_to_create:
            existing_perm = Permission.query.filter_by(name=perm_data["name"]).first()
            if not existing_perm:
                permission = Permission(
                    name=perm_data["name"],
                    description=perm_data["description"]
                )
                db.session.add(permission)
                print(f"  ✅ Created permission: {perm_data['name']}")
            else:
                print(f"  ⚠️  Permission already exists: {perm_data['name']}")

        db.session.commit()

        # Assign permissions to roles
        assign_permissions_to_roles()

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error initializing roles: {e}")


def assign_permissions_to_roles():
    """Assign permissions to roles"""
    try:
        # Get roles
        owner_role = Role.query.filter_by(name="project_owner").first()
        member_role = Role.query.filter_by(name="project_member").first()
        viewer_role = Role.query.filter_by(name="project_viewer").first()

        # Get permissions
        all_permissions = Permission.query.all()

        # Owner gets all permissions
        if owner_role:
            for permission in all_permissions:
                existing = RolePermission.query.filter_by(
                    role_id=owner_role.id,
                    permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(
                        role_id=owner_role.id,
                        permission_id=permission.id
                    )
                    db.session.add(role_perm)
            print("  ✅ Assigned all permissions to project_owner")

        # Member gets limited permissions
        if member_role:
            member_permission_names = [
                "view_project", "create_task", "edit_task", "assign_task",
                "add_comment", "edit_comment", "upload_attachment"
            ]
            for perm_name in member_permission_names:
                permission = Permission.query.filter_by(name=perm_name).first()
                if permission:
                    existing = RolePermission.query.filter_by(
                        role_id=member_role.id,
                        permission_id=permission.id
                    ).first()
                    if not existing:
                        role_perm = RolePermission(
                            role_id=member_role.id,
                            permission_id=permission.id
                        )
                        db.session.add(role_perm)
            print("  ✅ Assigned limited permissions to project_member")

        # Viewer gets only view permission
        if viewer_role:
            view_permission = Permission.query.filter_by(name="view_project").first()
            if view_permission:
                existing = RolePermission.query.filter_by(
                    role_id=viewer_role.id,
                    permission_id=view_permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(
                        role_id=viewer_role.id,
                        permission_id=view_permission.id
                    )
                    db.session.add(role_perm)
            print("  ✅ Assigned view permission to project_viewer")

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error assigning permissions: {e}")


def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    migrate.init_app(app, db)
    return app

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        migrations_dir = Path("migrations")
        versions_dir = migrations_dir / "versions"

        if not migrations_dir.exists():
            print("🔧 Initializing migrations folder...")
            flask_init()
            versions_dir.mkdir(parents=True, exist_ok=True)
            stamp()  # đặt mốc version để tránh lỗi khi migrate
        elif not versions_dir.exists():
            versions_dir.mkdir(parents=True, exist_ok=True)

        print("📦 Generating migration script...")
        flask_migrate(message="initial")

        print("🚀 Applying migration...")
        upgrade()

        print("✅ Migration complete.")

        # Initialize basic roles and permissions
        print("🔧 Initializing basic roles and permissions...")
        init_basic_roles_and_permissions()
        print("✅ Roles and permissions initialized.")


