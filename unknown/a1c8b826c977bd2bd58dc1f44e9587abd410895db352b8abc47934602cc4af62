# 📚 TMS API Structure Documentation

## 🏗️ Folder Structure

```
app/
├── auth/                   # Authentication & Authorization
│   ├── routes.py          # Login, register, logout endpoints
│   └── services.py        # Auth business logic
├── projects/              # Project Management
│   ├── entities.py        # Project entity (business objects)
│   ├── permissions.py     # Project permission system
│   ├── routes.py          # Project CRUD endpoints
│   └── services.py        # Project business logic
├── members/               # Project Member Management
│   ├── routes.py          # Member management endpoints
│   └── services.py        # Member business logic
├── statistics/            # Statistics & Analytics
│   ├── routes.py          # Statistics endpoints
│   └── services.py        # Statistics business logic
├── tasks/                 # Task Management
│   ├── routes.py          # Task CRUD endpoints
│   └── services.py        # Task business logic
├── users/                 # User Management
│   ├── routes.py          # User endpoints
│   └── services.py        # User business logic
└── helpers/               # Shared utilities
    ├── extensions.py      # Flask extensions
    ├── permissions.py     # Permission decorators
    └── validators.py      # Data validators
```

## 🔗 API Endpoints

### 🔐 Authentication (`/auth/`)
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user info

### 📁 Projects (`/projects/`)
- `GET /projects/` - List all projects for user
- `POST /projects/` - Create new project
- `GET /projects/{id}` - Get specific project
- `PUT /projects/{id}` - Update project
- `DELETE /projects/{id}` - Delete project (soft delete)
- `POST /projects/{id}/restore` - Restore deleted project

### 👥 Members (`/members/`)
- `GET /members/projects/{project_id}` - Get project members
- `POST /members/projects/{project_id}` - Add member to project
- `PUT /members/projects/{project_id}/users/{user_id}` - Update member role
- `DELETE /members/projects/{project_id}/users/{user_id}` - Remove member
- `GET /members/my-projects` - Get user's projects

### 📊 Statistics (`/statistics/`)
- `GET /statistics/dashboard` - Get dashboard stats for current user
- `GET /statistics/user` - Get detailed user statistics
- `GET /statistics/user/{user_id}` - Get specific user statistics
- `GET /statistics/project/{project_id}` - Get project statistics

### ✅ Tasks (`/tasks/`)
- `GET /tasks/list_tasks` - List tasks with filters
- `POST /tasks/create_task` - Create new task
- `PUT /tasks/update_task/{id}` - Update task
- `DELETE /tasks/delete_task_view/{id}` - Delete task

### 👤 Users (`/users/`)
- `GET /users` - List users

## 🔒 Permission System

### Project Roles
- **`project_owner`** - Full access (project creator)
- **`project_member`** - Can create/edit tasks, add comments
- **`project_viewer`** - Read-only access

### Project Permissions
- `view_project` - View project details
- `edit_project` - Edit project settings
- `delete_project` - Delete project
- `manage_project_members` - Add/remove members
- `create_task` - Create new tasks
- `edit_task` - Edit tasks
- `delete_task` - Delete tasks
- `assign_task` - Assign tasks to users
- `add_comment` - Add comments
- `edit_comment` - Edit comments
- `delete_comment` - Delete comments
- `upload_attachment` - Upload files
- `delete_attachment` - Delete files

## 📈 Statistics Features

### Dashboard Statistics
- Total projects (created + member of)
- Active vs completed projects
- Total tasks in user's projects
- User's assigned tasks (pending, completed, overdue)
- Recent activity (last 7 days)

### User Statistics
- Projects created vs member of
- Tasks created vs assigned
- Task completion rate
- Recent activity metrics

### Project Statistics
- Task breakdown by status (To Do, In Progress, Done)
- Task priority distribution
- Overdue tasks count
- Time estimation tracking
- Team member count
- Progress percentage
- Recent activity

## 🏛️ Database Models

### Core Models
- **User** - User accounts
- **Project** - Projects with status, priority, dates
- **Task** - Tasks with assignments and tracking
- **UserProject** - User-project relationships with roles
- **Role** - Permission roles
- **Permission** - Individual permissions
- **RolePermission** - Role-permission mappings

### Key Relationships
- User ↔ Project (many-to-many via UserProject)
- Project → Tasks (one-to-many)
- User → Tasks (assignee, creator)
- Role ↔ Permission (many-to-many via RolePermission)

## 🔧 Entity Pattern

The system uses **Entity Pattern** to separate business logic from database models:

- **Entities** (`entities.py`) - Business objects with validation
- **Services** (`services.py`) - Business logic and operations
- **Models** (`models.py`) - Database representations
- **Routes** (`routes.py`) - API endpoints and HTTP handling

## 🚀 Usage Examples

### Create Project with Members
```python
# 1. Create project
POST /projects/
{
  "name": "My Project",
  "description": "Project description",
  "status": "active",
  "priority": "high"
}

# 2. Add member
POST /members/projects/{project_id}
{
  "user_id": 2,
  "role": "project_member"
}

# 3. Get statistics
GET /statistics/project/{project_id}
```

### Permission Checking
```python
# Check if user can manage members
if ProjectPermissionChecker.has_project_permission(
    user_id, project_id, ProjectPermission.MANAGE_MEMBERS
):
    # Allow member management
```

## 🔍 Testing

Use the provided test scripts:
- `test_projects_api.py` - Test project CRUD
- `test_complete_api.py` - Test complete system

Or test manually via Swagger UI at: `http://localhost:5001/apidocs/`

## 📝 Notes

- All endpoints require JWT authentication (except register/login)
- Soft delete is used for projects (can be restored)
- Permission checking is enforced at service level
- Statistics are calculated in real-time
- Entity validation ensures data integrity
